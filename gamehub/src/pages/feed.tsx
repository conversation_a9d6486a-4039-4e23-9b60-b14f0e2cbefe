import { useState, useEffect, useRef } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Divider,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Image,
  Tabs,
  Tab,
  Select,
  SelectItem,
  Progress,
  Badge,
  Tooltip,
  Skeleton,
} from '@heroui/react';
import {
  PlusIcon,
  HeartIcon,
  ShareIcon,
  ChatBubbleLeftIcon,
  EllipsisHorizontalIcon,
  PhotoIcon,
  VideoCameraIcon,
  LinkIcon,
  FaceSmileIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  FireIcon,
  ClockIcon,
  SparklesIcon,
  TrophyIcon,
  StarIcon,
  PlayIcon,
  XMarkIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  EyeIcon,
  BookmarkIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
  BookmarkIcon as BookmarkSolidIcon,
  StarIcon as StarSolidIcon,
} from '@heroicons/react/24/solid';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { useAuth } from '@/contexts/AuthContext';
import { Post, Comment } from '@/types/community';
import { PostsService } from '@/services/communityService';

// Reaction types
const REACTION_TYPES = {
  like: { emoji: '👍', label: 'Curtir', color: 'text-blue-500' },
  love: { emoji: '❤️', label: 'Amar', color: 'text-red-500' },
  laugh: { emoji: '😂', label: 'Rir', color: 'text-yellow-500' },
  wow: { emoji: '😮', label: 'Uau', color: 'text-purple-500' },
  sad: { emoji: '😢', label: 'Triste', color: 'text-blue-400' },
  angry: { emoji: '😠', label: 'Raiva', color: 'text-red-600' },
  fire: { emoji: '🔥', label: 'Incrível', color: 'text-orange-500' },
  clap: { emoji: '👏', label: 'Parabéns', color: 'text-green-500' },
};

// Mock data for demonstration
const mockPosts: Post[] = [
  {
    id: '1',
    authorId: 'user1',
    authorName: 'GamerPro2024',
    authorAvatar: 'https://i.pravatar.cc/150?u=user1',
    content: 'Acabei de terminar Elden Ring pela terceira vez! Que jogo incrível. A cada playthrough descubro algo novo. Alguém mais viciado nesse mundo? 🎮✨',
    type: 'text',
    gameId: 'elden-ring',
    gameName: 'Elden Ring',
    tags: ['elden-ring', 'souls-like', 'rpg'],
    likesCount: 124,
    commentsCount: 18,
    sharesCount: 7,
    createdAt: { seconds: Date.now() / 1000 - 3600 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '2',
    authorId: 'user2',
    authorName: 'StreamerQueen',
    authorAvatar: 'https://i.pravatar.cc/150?u=user2',
    content: 'Live hoje às 20h jogando Valorant! Vamos subir de rank juntos? Drop no chat quem vai aparecer! 🔥',
    type: 'text',
    gameId: 'valorant',
    gameName: 'Valorant',
    tags: ['valorant', 'stream', 'live'],
    likesCount: 89,
    commentsCount: 23,
    sharesCount: 12,
    createdAt: { seconds: Date.now() / 1000 - 7200 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 7200 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '3',
    authorId: 'user3',
    authorName: 'RetroGamer90',
    authorAvatar: 'https://i.pravatar.cc/150?u=user3',
    content: 'Encontrei essa gem escondida na Steam! Hollow Knight continua sendo uma obra-prima. Se você curte metroidvania, não pode perder.',
    type: 'image',
    gameId: 'hollow-knight',
    gameName: 'Hollow Knight',
    images: ['/api/placeholder/600/400'],
    tags: ['hollow-knight', 'metroidvania', 'indie'],
    likesCount: 67,
    commentsCount: 15,
    sharesCount: 8,
    createdAt: { seconds: Date.now() / 1000 - 14400 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 14400 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '4',
    authorId: 'user4',
    authorName: 'CompetitivePlayer',
    authorAvatar: 'https://i.pravatar.cc/150?u=user4',
    content: 'Finalmente cheguei ao Diamante no Valorant! 💎 Foi uma jornada longa, mas valeu cada partida. Dicas para quem está subindo: comunicação é TUDO!',
    type: 'text',
    gameId: 'valorant',
    gameName: 'Valorant',
    tags: ['valorant', 'competitive', 'rank', 'diamante'],
    likesCount: 156,
    commentsCount: 34,
    sharesCount: 19,
    createdAt: { seconds: Date.now() / 1000 - 21600 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 21600 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '5',
    authorId: 'user5',
    authorName: 'IndieExplorer',
    authorAvatar: 'https://i.pravatar.cc/150?u=user5',
    content: 'Lista dos melhores jogos indie de 2024 que vocês PRECISAM jogar! Cada um é uma experiência única. Qual vocês já jogaram?',
    type: 'link',
    linkUrl: 'https://example.com/best-indie-games-2024',
    linkTitle: 'Os 10 Melhores Jogos Indie de 2024',
    linkDescription: 'Uma curadoria especial dos jogos independentes que marcaram o ano',
    tags: ['indie', 'games', '2024', 'recomendacao'],
    likesCount: 203,
    commentsCount: 45,
    sharesCount: 28,
    createdAt: { seconds: Date.now() / 1000 - 28800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 28800 } as any,
    isEdited: false,
    visibility: 'public',
  },
];

const mockComments: Comment[] = [
  {
    id: 'c1',
    postId: '1',
    authorId: 'user6',
    authorName: 'SoulsVeteran',
    authorAvatar: 'https://i.pravatar.cc/150?u=user6',
    content: 'Elden Ring é realmente viciante! Estou na minha segunda run e ainda descobrindo segredos.',
    likesCount: 12,
    repliesCount: 2,
    createdAt: { seconds: Date.now() / 1000 - 1800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 1800 } as any,
    isEdited: false,
  },
  {
    id: 'c2',
    postId: '1',
    authorId: 'user7',
    authorName: 'MagicUser',
    authorAvatar: 'https://i.pravatar.cc/150?u=user7',
    content: 'Qual build vocês recomendam para iniciantes? Estou começando agora.',
    likesCount: 8,
    repliesCount: 5,
    createdAt: { seconds: Date.now() / 1000 - 900 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 900 } as any,
    isEdited: false,
  },
];

export default function FeedPage() {
  const { currentUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isShareOpen, onOpen: onShareOpen, onClose: onShareClose } = useDisclosure();
  const [sharePost, setSharePost] = useState<Post | null>(null);
  const [shareComment, setShareComment] = useState('');
  
  const [posts, setPosts] = useState<Post[]>(mockPosts);
  const [comments, setComments] = useState<Comment[]>(mockComments);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('recent');
  const [searchTerm, setSearchTerm] = useState('');
  const [likedPosts, setLikedPosts] = useState<string[]>([]);
  const [expandedComments, setExpandedComments] = useState<string[]>([]);
  const [newComment, setNewComment] = useState<{ [key: string]: string }>({});
  const [postReactions, setPostReactions] = useState<{ [key: string]: { [key: string]: number } }>({});
  const [userReactions, setUserReactions] = useState<{ [key: string]: string }>({});
  const [showReactionPicker, setShowReactionPicker] = useState<string | null>(null);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [expandedReplies, setExpandedReplies] = useState<string[]>([]);
  const [commentReactions, setCommentReactions] = useState<{ [key: string]: { [key: string]: number } }>({});
  const [userCommentReactions, setUserCommentReactions] = useState<{ [key: string]: string }>({});
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(1);
  const [followingOnly, setFollowingOnly] = useState(false);
  const [gameFilter, setGameFilter] = useState('');
  const [contentTypeFilter, setContentTypeFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  const [trendingTags, setTrendingTags] = useState<{ tag: string; count: number }[]>([]);
  const [suggestedUsers, setSuggestedUsers] = useState<any[]>([]);
  const [trendingPosts, setTrendingPosts] = useState<Post[]>([]);
  const [blockedUsers, setBlockedUsers] = useState<string[]>([]);
  const [reportedContent, setReportedContent] = useState<string[]>([]);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportTarget, setReportTarget] = useState<{ id: string; type: 'post' | 'comment'; authorId: string } | null>(null);
  const [reportReason, setReportReason] = useState('');
  const [reportDescription, setReportDescription] = useState('');
  
  // New post form
  const [newPost, setNewPost] = useState({
    content: '',
    type: 'text' as 'text' | 'image' | 'video' | 'link' | 'achievement' | 'review',
    gameId: '',
    gameName: '',
    tags: '',
    linkUrl: '',
    linkTitle: '',
    linkDescription: '',
    visibility: 'public',
    images: [] as string[],
    videoUrl: '',
    rating: 0,
    achievementTitle: '',
    achievementDescription: '',
    reviewScore: 0,
  });

  const [uploadingMedia, setUploadingMedia] = useState(false);
  const [mediaPreview, setMediaPreview] = useState<string[]>([]);
  const [linkPreview, setLinkPreview] = useState<{
    title: string;
    description: string;
    image: string;
    url: string;
  } | null>(null);
  const [loadingLinkPreview, setLoadingLinkPreview] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [bookmarkedPosts, setBookmarkedPosts] = useState<string[]>([]);
  const [sharedPosts, setSharedPosts] = useState<string[]>([]);

  useEffect(() => {
    loadFeed();
  }, [selectedFilter, selectedSort, gameFilter, contentTypeFilter, timeFilter, searchTerm]);

  const calculatePostScore = (post: Post) => {
    const now = Date.now() / 1000;
    const postAge = now - post.createdAt.seconds;
    const ageInHours = postAge / 3600;

    // Base engagement score
    const engagementScore = post.likesCount * 1 + post.commentsCount * 2 + post.sharesCount * 3;

    // Time decay factor (posts lose relevance over time)
    const timeDecay = Math.exp(-ageInHours / 24); // Half-life of 24 hours

    // User preference boost (simulate based on user's interests)
    let preferenceBoost = 1;
    if (currentUser) {
      // Boost posts from games user is interested in
      if (post.gameId && ['elden-ring', 'valorant', 'minecraft'].includes(post.gameId)) {
        preferenceBoost *= 1.5;
      }
      // Boost posts with tags user likes
      if (post.tags.some(tag => ['rpg', 'fps', 'multiplayer'].includes(tag))) {
        preferenceBoost *= 1.3;
      }
    }

    // Content type boost
    const typeBoosts = {
      'achievement': 1.2,
      'review': 1.1,
      'image': 1.15,
      'video': 1.25,
      'text': 1.0,
      'link': 0.9
    };
    const typeBoost = typeBoosts[post.type] || 1.0;

    return engagementScore * timeDecay * preferenceBoost * typeBoost;
  };

  const loadFeed = async (loadMore = false) => {
    if (loading) return;

    setLoading(true);

    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      let filteredPosts = [...mockPosts];

      // Apply filters
      if (selectedFilter !== 'all') {
        filteredPosts = filteredPosts.filter(post =>
          post.gameId === selectedFilter ||
          post.tags.includes(selectedFilter)
        );
      }

      if (gameFilter) {
        filteredPosts = filteredPosts.filter(post =>
          post.gameName?.toLowerCase().includes(gameFilter.toLowerCase()) ||
          post.gameId?.toLowerCase().includes(gameFilter.toLowerCase())
        );
      }

      if (contentTypeFilter !== 'all') {
        filteredPosts = filteredPosts.filter(post => post.type === contentTypeFilter);
      }

      if (timeFilter !== 'all') {
        const now = Date.now() / 1000;
        const timeFilters = {
          'today': 24 * 3600,
          'week': 7 * 24 * 3600,
          'month': 30 * 24 * 3600
        };
        const timeLimit = timeFilters[timeFilter as keyof typeof timeFilters];
        if (timeLimit) {
          filteredPosts = filteredPosts.filter(post =>
            now - post.createdAt.seconds < timeLimit
          );
        }
      }

      if (searchTerm) {
        filteredPosts = filteredPosts.filter(post =>
          post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
          post.authorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
          post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        );
      }

      // Apply algorithm-based sorting
      if (selectedSort === 'algorithm') {
        filteredPosts = filteredPosts.map(post => ({
          ...post,
          score: calculatePostScore(post)
        })).sort((a: any, b: any) => b.score - a.score);
      } else if (selectedSort === 'recent') {
        filteredPosts.sort((a, b) => b.createdAt.seconds - a.createdAt.seconds);
      } else if (selectedSort === 'popular') {
        filteredPosts.sort((a, b) =>
          (b.likesCount + b.commentsCount + b.sharesCount) -
          (a.likesCount + a.commentsCount + a.sharesCount)
        );
      }

      // Simulate pagination
      const postsPerPage = 10;
      const startIndex = loadMore ? posts.length : 0;
      const endIndex = startIndex + postsPerPage;
      const newPosts = filteredPosts.slice(startIndex, endIndex);

      if (loadMore) {
        setPosts(prev => [...prev, ...newPosts]);
      } else {
        setPosts(newPosts);
      }

      setHasMore(endIndex < filteredPosts.length);
      setPage(prev => loadMore ? prev + 1 : 1);

    } catch (error) {
      console.error('Error loading feed:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMorePosts = () => {
    if (!loading && hasMore) {
      loadFeed(true);
    }
  };

  const calculateTrendingData = () => {
    // Calculate trending tags
    const tagCounts: { [key: string]: number } = {};
    mockPosts.forEach(post => {
      post.tags.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + post.likesCount + post.commentsCount + post.sharesCount;
      });
    });

    const trending = Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([tag, count]) => ({ tag, count }));

    setTrendingTags(trending);

    // Calculate trending posts (last 24 hours with high engagement)
    const now = Date.now() / 1000;
    const dayAgo = now - (24 * 3600);

    const recentPosts = mockPosts.filter(post => post.createdAt.seconds > dayAgo);
    const trending_posts = recentPosts
      .sort((a, b) => {
        const scoreA = a.likesCount * 1 + a.commentsCount * 2 + a.sharesCount * 3;
        const scoreB = b.likesCount * 1 + b.commentsCount * 2 + b.sharesCount * 3;
        return scoreB - scoreA;
      })
      .slice(0, 5);

    setTrendingPosts(trending_posts);

    // Mock suggested users
    const suggested = [
      {
        id: 'user1',
        name: 'ProGamer2024',
        avatar: 'https://i.pravatar.cc/150?u=user1',
        bio: 'Streamer de Valorant e CS2',
        followers: 1250,
        isFollowing: false
      },
      {
        id: 'user2',
        name: 'RPGMaster',
        avatar: 'https://i.pravatar.cc/150?u=user2',
        bio: 'Especialista em RPGs e Souls-like',
        followers: 890,
        isFollowing: false
      },
      {
        id: 'user3',
        name: 'IndieGameDev',
        avatar: 'https://i.pravatar.cc/150?u=user3',
        bio: 'Desenvolvedor indie e reviewer',
        followers: 2100,
        isFollowing: false
      }
    ];

    setSuggestedUsers(suggested);
  };

  useEffect(() => {
    calculateTrendingData();
  }, []);

  const handleBlockUser = (userId: string) => {
    if (!currentUser || userId === currentUser.uid) return;

    setBlockedUsers(prev => [...prev, userId]);

    // Remove posts from blocked user
    setPosts(prev => prev.filter(post => post.authorId !== userId));

    // Remove comments from blocked user
    setComments(prev => prev.filter(comment => comment.authorId !== userId));

    console.log('User blocked:', userId);
  };

  const handleUnblockUser = (userId: string) => {
    setBlockedUsers(prev => prev.filter(id => id !== userId));
    console.log('User unblocked:', userId);
  };

  const openReportModal = (id: string, type: 'post' | 'comment', authorId: string) => {
    setReportTarget({ id, type, authorId });
    setShowReportModal(true);
  };

  const handleReport = () => {
    if (!reportTarget || !reportReason.trim()) return;

    setReportedContent(prev => [...prev, reportTarget.id]);

    // In production, send report to moderation system
    console.log('Content reported:', {
      targetId: reportTarget.id,
      type: reportTarget.type,
      authorId: reportTarget.authorId,
      reason: reportReason,
      description: reportDescription,
      reportedBy: currentUser?.uid
    });

    // Reset modal
    setReportReason('');
    setReportDescription('');
    setReportTarget(null);
    setShowReportModal(false);
  };

  const isContentHidden = (authorId: string, contentId: string) => {
    return blockedUsers.includes(authorId) || reportedContent.includes(contentId);
  };

  const REPORT_REASONS = [
    'Spam ou conteúdo repetitivo',
    'Assédio ou bullying',
    'Conteúdo ofensivo ou inadequado',
    'Informações falsas',
    'Violação de direitos autorais',
    'Conteúdo sexual inapropriado',
    'Violência ou ameaças',
    'Outro motivo'
  ];

  const handleCreatePost = async () => {
    if (!currentUser || !newPost.content.trim()) return;

    const post: Post = {
      id: `post-${Date.now()}`,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newPost.content,
      type: newPost.type,
      gameId: newPost.gameId || undefined,
      gameName: newPost.gameName || undefined,
      images: newPost.images.length > 0 ? newPost.images : undefined,
      videoUrl: newPost.videoUrl || undefined,
      linkUrl: newPost.linkUrl || undefined,
      linkTitle: newPost.linkTitle || undefined,
      linkDescription: newPost.linkDescription || undefined,
      tags: newPost.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      likesCount: 0,
      commentsCount: 0,
      sharesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
      visibility: newPost.visibility as 'public' | 'followers' | 'friends',
    };

    setPosts(prev => [post, ...prev]);
    setNewPost({
      content: '',
      type: 'text',
      gameId: '',
      gameName: '',
      tags: '',
      linkUrl: '',
      linkTitle: '',
      linkDescription: '',
      visibility: 'public',
      images: [],
      videoUrl: '',
      rating: 0,
      achievementTitle: '',
      achievementDescription: '',
      reviewScore: 0,
    });
    setMediaPreview([]);
    setLinkPreview(null);
    onClose();
  };

  const handleReaction = (postId: string, reactionType: string) => {
    if (!currentUser) return;

    const currentReaction = userReactions[postId];

    // Initialize post reactions if not exists
    if (!postReactions[postId]) {
      setPostReactions(prev => ({
        ...prev,
        [postId]: {}
      }));
    }

    // Remove current reaction if exists
    if (currentReaction) {
      setPostReactions(prev => ({
        ...prev,
        [postId]: {
          ...prev[postId],
          [currentReaction]: Math.max(0, (prev[postId]?.[currentReaction] || 0) - 1)
        }
      }));
    }

    // Add new reaction if different from current
    if (currentReaction !== reactionType) {
      setPostReactions(prev => ({
        ...prev,
        [postId]: {
          ...prev[postId],
          [reactionType]: (prev[postId]?.[reactionType] || 0) + 1
        }
      }));
      setUserReactions(prev => ({
        ...prev,
        [postId]: reactionType
      }));
    } else {
      // Remove reaction if same as current
      setUserReactions(prev => {
        const newReactions = { ...prev };
        delete newReactions[postId];
        return newReactions;
      });
    }

    // Update total likes count for backward compatibility
    const totalReactions = Object.values(postReactions[postId] || {}).reduce((sum, count) => sum + count, 0);
    setPosts(prev => prev.map(post =>
      post.id === postId
        ? { ...post, likesCount: totalReactions }
        : post
    ));

    setShowReactionPicker(null);
  };

  const handleLikePost = (postId: string) => {
    handleReaction(postId, 'like');
  };

  const getTotalReactions = (postId: string) => {
    const reactions = postReactions[postId] || {};
    return Object.values(reactions).reduce((sum, count) => sum + count, 0);
  };

  const getTopReactions = (postId: string, limit: number = 3) => {
    const reactions = postReactions[postId] || {};
    return Object.entries(reactions)
      .filter(([_, count]) => count > 0)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit);
  };

  const handleAddComment = (postId: string, parentCommentId?: string) => {
    if (!currentUser || !newComment[postId]?.trim()) return;

    const comment: Comment = {
      id: `comment-${Date.now()}`,
      postId,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newComment[postId],
      parentCommentId,
      likesCount: 0,
      repliesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
    };

    setComments(prev => [...prev, comment]);

    if (parentCommentId) {
      // Update parent comment replies count
      setComments(prev => prev.map(c =>
        c.id === parentCommentId
          ? { ...c, repliesCount: c.repliesCount + 1 }
          : c
      ));
    } else {
      // Update post comments count
      setPosts(prev => prev.map(post =>
        post.id === postId
          ? { ...post, commentsCount: post.commentsCount + 1 }
          : post
      ));
    }

    setNewComment(prev => ({ ...prev, [postId]: '' }));
    setReplyingTo(null);
  };

  const handleCommentReaction = (commentId: string, reactionType: string) => {
    if (!currentUser) return;

    const currentReaction = userCommentReactions[commentId];

    // Initialize comment reactions if not exists
    if (!commentReactions[commentId]) {
      setCommentReactions(prev => ({
        ...prev,
        [commentId]: {}
      }));
    }

    // Remove current reaction if exists
    if (currentReaction) {
      setCommentReactions(prev => ({
        ...prev,
        [commentId]: {
          ...prev[commentId],
          [currentReaction]: Math.max(0, (prev[commentId]?.[currentReaction] || 0) - 1)
        }
      }));
    }

    // Add new reaction if different from current
    if (currentReaction !== reactionType) {
      setCommentReactions(prev => ({
        ...prev,
        [commentId]: {
          ...prev[commentId],
          [reactionType]: (prev[commentId]?.[reactionType] || 0) + 1
        }
      }));
      setUserCommentReactions(prev => ({
        ...prev,
        [commentId]: reactionType
      }));
    } else {
      // Remove reaction if same as current
      setUserCommentReactions(prev => {
        const newReactions = { ...prev };
        delete newReactions[commentId];
        return newReactions;
      });
    }

    // Update total likes count for backward compatibility
    const totalReactions = Object.values(commentReactions[commentId] || {}).reduce((sum, count) => sum + count, 0);
    setComments(prev => prev.map(comment =>
      comment.id === commentId
        ? { ...comment, likesCount: totalReactions }
        : comment
    ));
  };

  const getCommentReplies = (commentId: string) => {
    return comments.filter(comment => comment.parentCommentId === commentId);
  };

  const getTopLevelComments = (postId: string) => {
    return comments.filter(comment => comment.postId === postId && !comment.parentCommentId);
  };

  const toggleReplies = (commentId: string) => {
    setExpandedReplies(prev =>
      prev.includes(commentId)
        ? prev.filter(id => id !== commentId)
        : [...prev, commentId]
    );
  };

  const toggleComments = (postId: string) => {
    setExpandedComments(prev =>
      prev.includes(postId)
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const ReactionPicker = ({ postId, onClose }: { postId: string; onClose: () => void }) => (
    <div className="absolute bottom-full left-0 mb-2 bg-white dark:bg-gray-800 border border-divider rounded-full shadow-lg p-2 flex gap-1 z-10">
      {Object.entries(REACTION_TYPES).map(([type, config]) => (
        <Button
          key={type}
          variant="light"
          size="sm"
          isIconOnly
          className="hover:scale-110 transition-transform"
          onClick={() => {
            handleReaction(postId, type);
            onClose();
          }}
        >
          <span className="text-lg">{config.emoji}</span>
        </Button>
      ))}
    </div>
  );

  const ReactionSummary = ({ postId }: { postId: string }) => {
    const topReactions = getTopReactions(postId);
    const totalReactions = getTotalReactions(postId);

    if (totalReactions === 0) return null;

    return (
      <div className="flex items-center gap-2 text-sm text-default-500 mb-2">
        <div className="flex items-center gap-1">
          {topReactions.map(([type, count]) => (
            <div key={type} className="flex items-center gap-1">
              <span className="text-base">{REACTION_TYPES[type as keyof typeof REACTION_TYPES]?.emoji}</span>
              <span>{count}</span>
            </div>
          ))}
        </div>
        {totalReactions > 0 && (
          <span className="text-xs">
            {totalReactions} {totalReactions === 1 ? 'reação' : 'reações'}
          </span>
        )}
      </div>
    );
  };

  const CommentComponent = ({ comment, postId, depth = 0 }: { comment: Comment; postId: string; depth?: number }) => {
    const replies = getCommentReplies(comment.id);
    const hasReplies = replies.length > 0;
    const isExpanded = expandedReplies.includes(comment.id);
    const maxDepth = 3; // Maximum nesting depth

    return (
      <div className={`${depth > 0 ? 'ml-8 border-l-2 border-default-200 pl-4' : ''}`}>
        <div className="flex gap-3">
          <Avatar
            src={comment.authorAvatar}
            name={comment.authorName}
            size="sm"
          />
          <div className="flex-1">
            <div className="bg-default-100 rounded-lg p-3">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-semibold text-sm">
                  {comment.authorName}
                </span>
                <span className="text-xs text-default-500">
                  {formatTimeAgo(comment.createdAt)}
                </span>
              </div>
              <p className="text-sm">{comment.content}</p>
            </div>

            {/* Comment Actions */}
            <div className="flex items-center gap-4 mt-2 text-xs">
              <Button
                variant="light"
                size="sm"
                startContent={
                  userCommentReactions[comment.id] ? (
                    <span className="text-sm">
                      {REACTION_TYPES[userCommentReactions[comment.id] as keyof typeof REACTION_TYPES]?.emoji}
                    </span>
                  ) : (
                    <HeartIcon className="w-3 h-3" />
                  )
                }
                onClick={() => handleCommentReaction(comment.id, 'like')}
                className={`text-xs ${userCommentReactions[comment.id] ? 'text-primary' : ''}`}
              >
                {comment.likesCount > 0 && comment.likesCount}
              </Button>

              {depth < maxDepth && (
                <Button
                  variant="light"
                  size="sm"
                  onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                  className="text-xs"
                >
                  Responder
                </Button>
              )}

              {hasReplies && (
                <Button
                  variant="light"
                  size="sm"
                  onClick={() => toggleReplies(comment.id)}
                  className="text-xs"
                >
                  {isExpanded ? 'Ocultar' : 'Ver'} {replies.length} {replies.length === 1 ? 'resposta' : 'respostas'}
                </Button>
              )}
            </div>

            {/* Reply Input */}
            {replyingTo === comment.id && currentUser && (
              <div className="flex gap-2 mt-3">
                <Avatar
                  src={currentUser.photoURL || undefined}
                  name={currentUser.displayName || 'U'}
                  size="sm"
                />
                <div className="flex-1 flex gap-2">
                  <Input
                    placeholder="Escreva uma resposta..."
                    value={newComment[postId] || ''}
                    onChange={(e) => setNewComment(prev => ({
                      ...prev,
                      [postId]: e.target.value
                    }))}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleAddComment(postId, comment.id);
                      }
                    }}
                    size="sm"
                  />
                  <Button
                    size="sm"
                    color="primary"
                    onClick={() => handleAddComment(postId, comment.id)}
                    isDisabled={!newComment[postId]?.trim()}
                  >
                    Responder
                  </Button>
                </div>
              </div>
            )}

            {/* Nested Replies */}
            {hasReplies && isExpanded && (
              <div className="mt-4 space-y-3">
                {replies.map((reply) => (
                  <CommentComponent
                    key={reply.id}
                    comment={reply}
                    postId={postId}
                    depth={depth + 1}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const formatTimeAgo = (timestamp: any) => {
    const now = Date.now();
    const postTime = timestamp.seconds * 1000;
    const diff = now - postTime;

    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    setUploadingMedia(true);
    const uploadedUrls: string[] = [];

    try {
      for (const file of Array.from(files)) {
        // Simulate upload - in production, upload to Firebase Storage
        const url = URL.createObjectURL(file);
        uploadedUrls.push(url);
      }

      setNewPost(prev => ({
        ...prev,
        images: [...prev.images, ...uploadedUrls]
      }));
      setMediaPreview(prev => [...prev, ...uploadedUrls]);
    } catch (error) {
      console.error('Error uploading files:', error);
    } finally {
      setUploadingMedia(false);
    }
  };

  const removeMediaPreview = (index: number) => {
    setNewPost(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
    setMediaPreview(prev => prev.filter((_, i) => i !== index));
  };

  const fetchLinkPreview = async (url: string) => {
    if (!url.trim()) {
      setLinkPreview(null);
      return;
    }

    setLoadingLinkPreview(true);
    try {
      // Simulate link preview fetch - in production, use a service like LinkPreview API
      const preview = {
        title: 'Preview do Link',
        description: 'Esta é uma descrição simulada do link compartilhado.',
        image: 'https://via.placeholder.com/400x200',
        url: url
      };
      setLinkPreview(preview);
      setNewPost(prev => ({
        ...prev,
        linkTitle: preview.title,
        linkDescription: preview.description
      }));
    } catch (error) {
      console.error('Error fetching link preview:', error);
      setLinkPreview(null);
    } finally {
      setLoadingLinkPreview(false);
    }
  };

  const getPostComments = (postId: string) => {
    return comments.filter(comment => comment.postId === postId);
  };

  const handleBookmarkPost = (postId: string) => {
    if (!currentUser) return;

    const isBookmarked = bookmarkedPosts.includes(postId);

    if (isBookmarked) {
      setBookmarkedPosts(prev => prev.filter(id => id !== postId));
    } else {
      setBookmarkedPosts(prev => [...prev, postId]);
    }
  };

  const handleSharePost = async (postId: string, shareComment?: string) => {
    if (!currentUser) return;

    const post = posts.find(p => p.id === postId);
    if (!post) return;

    // Create a repost
    const repost: Post = {
      id: `repost-${Date.now()}`,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: shareComment || `Compartilhou um post de ${post.authorName}`,
      type: 'text',
      tags: [],
      likesCount: 0,
      commentsCount: 0,
      sharesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
      visibility: 'public',
      // Add reference to original post
      originalPost: post,
    };

    // Add repost to feed
    setPosts(prev => [repost, ...prev]);

    // Update original post share count
    setSharedPosts(prev => [...prev, postId]);
    setPosts(prev => prev.map(p =>
      p.id === postId
        ? { ...p, sharesCount: p.sharesCount + 1 }
        : p
    ));

    // Reset share modal
    setShareComment('');
    setSharePost(null);
    onShareClose();
  };

  const openShareModal = (post: Post) => {
    setSharePost(post);
    onShareOpen();
  };

  const handleExternalShare = (post: Post, platform: string) => {
    const shareUrl = `${window.location.origin}/post/${post.id}`;
    const shareText = `Confira este post de ${post.authorName}: ${post.content.substring(0, 100)}...`;

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`);
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`);
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(shareText + ' ' + shareUrl)}`);
        break;
      case 'copy':
        navigator.clipboard.writeText(shareUrl);
        break;
    }
  };

  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <FireIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Feed da Comunidade
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Feed{" "}
              <span className={title({ color: "violet", size: "lg" })}>Social</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Acompanhe as últimas novidades, compartilhe suas experiências
              e conecte-se com outros gamers da comunidade.
            </p>
          </div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          {/* Left Sidebar - Filters */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <h3 className="font-semibold">Filtros</h3>
              </CardHeader>
              <CardBody className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Buscar</label>
                  <Input
                    placeholder="Buscar posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                    size="sm"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Categoria</label>
                  <Select
                    placeholder="Todas as categorias"
                    selectedKeys={[selectedFilter]}
                    onSelectionChange={(keys) => setSelectedFilter(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="all">Todas</SelectItem>
                    <SelectItem key="valorant">Valorant</SelectItem>
                    <SelectItem key="elden-ring">Elden Ring</SelectItem>
                    <SelectItem key="hollow-knight">Hollow Knight</SelectItem>
                    <SelectItem key="indie">Jogos Indie</SelectItem>
                    <SelectItem key="competitive">Competitivo</SelectItem>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Ordenar por</label>
                  <Select
                    selectedKeys={[selectedSort]}
                    onSelectionChange={(keys) => setSelectedSort(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="algorithm">Algoritmo (Para Você)</SelectItem>
                    <SelectItem key="recent">Mais recentes</SelectItem>
                    <SelectItem key="popular">Mais populares</SelectItem>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Tipo de Conteúdo</label>
                  <Select
                    selectedKeys={[contentTypeFilter]}
                    onSelectionChange={(keys) => setContentTypeFilter(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="all">Todos os tipos</SelectItem>
                    <SelectItem key="text">Texto</SelectItem>
                    <SelectItem key="image">Imagens</SelectItem>
                    <SelectItem key="video">Vídeos</SelectItem>
                    <SelectItem key="link">Links</SelectItem>
                    <SelectItem key="achievement">Conquistas</SelectItem>
                    <SelectItem key="review">Reviews</SelectItem>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Período</label>
                  <Select
                    selectedKeys={[timeFilter]}
                    onSelectionChange={(keys) => setTimeFilter(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="all">Todo o tempo</SelectItem>
                    <SelectItem key="today">Hoje</SelectItem>
                    <SelectItem key="week">Esta semana</SelectItem>
                    <SelectItem key="month">Este mês</SelectItem>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">Filtro de Jogo</label>
                  <Input
                    placeholder="Filtrar por jogo..."
                    value={gameFilter}
                    onChange={(e) => setGameFilter(e.target.value)}
                    size="sm"
                    startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                  />
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Main Feed */}
          <div className="lg:col-span-3 space-y-6">
            {/* Create Post */}
            {currentUser && (
              <Card>
                <CardBody className="p-4">
                  <div className="flex items-center gap-4">
                    <Avatar
                      src={currentUser.photoURL || undefined}
                      name={currentUser.displayName || 'U'}
                      size="md"
                    />
                    <Button
                      variant="flat"
                      className="flex-1 justify-start text-default-500"
                      onClick={onOpen}
                    >
                      O que você está jogando hoje?
                    </Button>
                    <Button
                      color="primary"
                      startContent={<PlusIcon className="w-4 h-4" />}
                      onClick={onOpen}
                    >
                      Criar Post
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Posts Feed */}
            <div className="space-y-6">
              {posts
                .filter(post => !isContentHidden(post.authorId, post.id))
                .map((post) => (
                <Card key={post.id} className="w-full">
                  <CardBody className="p-6">
                    {/* Repost Header */}
                    {(post as any).originalPost && (
                      <div className="flex items-center gap-2 mb-3 text-sm text-default-500">
                        <ArrowPathIcon className="w-4 h-4" />
                        <span>{post.authorName} compartilhou</span>
                      </div>
                    )}

                    {/* Post Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={(post as any).originalPost ? (post as any).originalPost.authorAvatar : post.authorAvatar}
                          name={(post as any).originalPost ? (post as any).originalPost.authorName : post.authorName}
                          size="md"
                        />
                        <div>
                          <p className="font-semibold">
                            {(post as any).originalPost ? (post as any).originalPost.authorName : post.authorName}
                          </p>
                          <div className="flex items-center gap-2 text-sm text-default-500">
                            <span>
                              {formatTimeAgo((post as any).originalPost ? (post as any).originalPost.createdAt : post.createdAt)}
                            </span>
                            {((post as any).originalPost ? (post as any).originalPost.gameName : post.gameName) && (
                              <>
                                <span>•</span>
                                <Chip size="sm" color="primary" variant="flat">
                                  {(post as any).originalPost ? (post as any).originalPost.gameName : post.gameName}
                                </Chip>
                              </>
                            )}
                            {((post as any).originalPost ? (post as any).originalPost.isEdited : post.isEdited) && (
                              <>
                                <span>•</span>
                                <span className="text-xs">editado</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>

                      <Dropdown>
                        <DropdownTrigger>
                          <Button
                            isIconOnly
                            variant="light"
                            size="sm"
                          >
                            <EllipsisHorizontalIcon className="w-4 h-4" />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu>
                          {currentUser?.uid === post.authorId ? (
                            <>
                              <DropdownItem
                                key="edit"
                                startContent={<span>✏️</span>}
                              >
                                Editar Post
                              </DropdownItem>
                              <DropdownItem
                                key="delete"
                                startContent={<span>🗑️</span>}
                                className="text-danger"
                                color="danger"
                              >
                                Excluir Post
                              </DropdownItem>
                            </>
                          ) : (
                            <>
                              <DropdownItem
                                key="follow"
                                startContent={<span>👤</span>}
                              >
                                Seguir {post.authorName}
                              </DropdownItem>
                              <DropdownItem
                                key="block"
                                startContent={<span>🚫</span>}
                                onClick={() => handleBlockUser(post.authorId)}
                                className="text-warning"
                                color="warning"
                              >
                                Bloquear Usuário
                              </DropdownItem>
                              <DropdownItem
                                key="report"
                                startContent={<span>⚠️</span>}
                                onClick={() => openReportModal(post.id, 'post', post.authorId)}
                                className="text-danger"
                                color="danger"
                              >
                                Reportar Post
                              </DropdownItem>
                            </>
                          )}
                          <DropdownItem
                            key="copy-link"
                            startContent={<span>🔗</span>}
                            onClick={() => {
                              const url = `${window.location.origin}/post/${post.id}`;
                              navigator.clipboard.writeText(url);
                            }}
                          >
                            Copiar Link
                          </DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>

                    {/* Post Content */}
                    <div className="mb-4">
                      {/* Repost Comment */}
                      {(post as any).originalPost && post.content && (
                        <div className="mb-4 p-3 bg-default-50 rounded-lg border-l-4 border-primary">
                          <div className="flex items-center gap-2 mb-2">
                            <Avatar
                              src={post.authorAvatar}
                              name={post.authorName}
                              size="sm"
                            />
                            <span className="font-semibold text-sm">{post.authorName}</span>
                            <span className="text-xs text-default-500">comentou:</span>
                          </div>
                          <p className="text-sm text-default-700">{post.content}</p>
                        </div>
                      )}

                      {/* Achievement Header */}
                      {((post as any).originalPost ? (post as any).originalPost.type : post.type) === 'achievement' && (
                        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 mb-3">
                          <div className="flex items-center gap-3">
                            <TrophyIcon className="w-8 h-8 text-yellow-600" />
                            <div>
                              <h4 className="font-bold text-yellow-800">Nova Conquista Desbloqueada!</h4>
                              <p className="text-yellow-700 text-sm">{post.gameName}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Review Header */}
                      {post.type === 'review' && (
                        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4 mb-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <StarIcon className="w-8 h-8 text-blue-600" />
                              <div>
                                <h4 className="font-bold text-blue-800">Review: {post.gameName}</h4>
                                <div className="flex items-center gap-1">
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <StarSolidIcon
                                      key={star}
                                      className={`w-4 h-4 ${
                                        star <= (post.rating || 0) ? 'text-yellow-500' : 'text-gray-300'
                                      }`}
                                    />
                                  ))}
                                  <span className="text-sm text-blue-700 ml-2">
                                    {post.rating || 0}/5
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <p className="text-default-700 leading-relaxed mb-3">
                        {(post as any).originalPost ? (post as any).originalPost.content : post.content}
                      </p>

                      {/* Post Media - Multiple Images */}
                      {post.images && post.images.length > 0 && (
                        <div className={`grid gap-2 mb-3 ${
                          post.images.length === 1 ? 'grid-cols-1' :
                          post.images.length === 2 ? 'grid-cols-2' :
                          'grid-cols-2 md:grid-cols-3'
                        }`}>
                          {post.images.map((image, index) => (
                            <Image
                              key={index}
                              src={image}
                              alt={`Post image ${index + 1}`}
                              className="w-full h-48 object-cover rounded-lg cursor-pointer hover:opacity-80 transition-opacity"
                              onClick={() => {
                                // In production, open image in modal/lightbox
                                window.open(image, '_blank');
                              }}
                            />
                          ))}
                        </div>
                      )}

                      {/* Video */}
                      {post.type === 'video' && post.videoUrl && (
                        <div className="mb-3">
                          <Card>
                            <CardBody className="p-4">
                              <div className="flex items-center gap-3 mb-3">
                                <PlayIcon className="w-6 h-6 text-primary" />
                                <span className="font-semibold">Vídeo Compartilhado</span>
                              </div>
                              <Button
                                color="primary"
                                variant="flat"
                                as="a"
                                href={post.videoUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                startContent={<PlayIcon className="w-4 h-4" />}
                                fullWidth
                              >
                                Assistir Vídeo
                              </Button>
                            </CardBody>
                          </Card>
                        </div>
                      )}

                      {/* Link Preview */}
                      {post.type === 'link' && post.linkUrl && (
                        <Card className="mb-3 border">
                          <CardBody className="p-4">
                            <div className="flex items-start gap-3">
                              <LinkIcon className="w-5 h-5 text-primary flex-shrink-0 mt-1" />
                              <div className="flex-1">
                                <h4 className="font-semibold text-sm mb-1">
                                  {post.linkTitle || post.linkUrl}
                                </h4>
                                {post.linkDescription && (
                                  <p className="text-sm text-default-600 mb-2">
                                    {post.linkDescription}
                                  </p>
                                )}
                                <p className="text-xs text-default-500 truncate mb-2">
                                  {post.linkUrl}
                                </p>
                                <Button
                                  size="sm"
                                  color="primary"
                                  variant="flat"
                                  as="a"
                                  href={post.linkUrl}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  startContent={<GlobeAltIcon className="w-3 h-3" />}
                                >
                                  Visitar Link
                                </Button>
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      )}
                    </div>

                    {/* Post Tags */}
                    {post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map((tag) => (
                          <Chip
                            key={tag}
                            size="sm"
                            variant="flat"
                            color="secondary"
                            className="text-xs cursor-pointer hover:bg-secondary/20"
                            onClick={() => setSearchTerm(tag)}
                          >
                            #{tag}
                          </Chip>
                        ))}
                      </div>
                    )}

                    <Divider className="my-4" />

                    {/* Reaction Summary */}
                    <ReactionSummary postId={post.id} />

                    {/* Post Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="relative">
                          <Button
                            variant="light"
                            size="sm"
                            startContent={
                              userReactions[post.id] ? (
                                <span className="text-base">
                                  {REACTION_TYPES[userReactions[post.id] as keyof typeof REACTION_TYPES]?.emoji}
                                </span>
                              ) : (
                                <HeartIcon className="w-4 h-4" />
                              )
                            }
                            onClick={() => handleLikePost(post.id)}
                            onMouseEnter={() => setShowReactionPicker(post.id)}
                            onMouseLeave={() => setTimeout(() => setShowReactionPicker(null), 300)}
                            className={userReactions[post.id] ? 'text-primary' : ''}
                          >
                            {getTotalReactions(post.id) || post.likesCount}
                          </Button>

                          {showReactionPicker === post.id && (
                            <ReactionPicker
                              postId={post.id}
                              onClose={() => setShowReactionPicker(null)}
                            />
                          )}
                        </div>
                        <Button
                          variant="light"
                          size="sm"
                          startContent={<ChatBubbleLeftIcon className="w-4 h-4" />}
                          onClick={() => toggleComments(post.id)}
                        >
                          {post.commentsCount}
                        </Button>

                        <Dropdown>
                          <DropdownTrigger>
                            <Button
                              variant="light"
                              size="sm"
                              startContent={<ShareIcon className="w-4 h-4" />}
                            >
                              {post.sharesCount}
                            </Button>
                          </DropdownTrigger>
                          <DropdownMenu aria-label="Compartilhar">
                            <DropdownItem
                              key="repost"
                              startContent={<ArrowPathIcon className="w-4 h-4" />}
                              onClick={() => openShareModal(post)}
                            >
                              Repostar com Comentário
                            </DropdownItem>
                            <DropdownItem
                              key="quick-repost"
                              startContent={<ArrowPathIcon className="w-4 h-4" />}
                              onClick={() => handleSharePost(post.id)}
                            >
                              Repostar Rapidamente
                            </DropdownItem>
                            <DropdownItem
                              key="twitter"
                              startContent={<span className="text-blue-500">🐦</span>}
                              onClick={() => handleExternalShare(post, 'twitter')}
                            >
                              Compartilhar no Twitter
                            </DropdownItem>
                            <DropdownItem
                              key="facebook"
                              startContent={<span className="text-blue-600">📘</span>}
                              onClick={() => handleExternalShare(post, 'facebook')}
                            >
                              Compartilhar no Facebook
                            </DropdownItem>
                            <DropdownItem
                              key="whatsapp"
                              startContent={<span className="text-green-500">💬</span>}
                              onClick={() => handleExternalShare(post, 'whatsapp')}
                            >
                              Compartilhar no WhatsApp
                            </DropdownItem>
                            <DropdownItem
                              key="copy"
                              startContent={<DocumentTextIcon className="w-4 h-4" />}
                              onClick={() => handleExternalShare(post, 'copy')}
                            >
                              Copiar Link
                            </DropdownItem>
                          </DropdownMenu>
                        </Dropdown>
                      </div>

                      <div className="flex items-center gap-2">
                        <Tooltip content={bookmarkedPosts.includes(post.id) ? 'Remover dos salvos' : 'Salvar post'}>
                          <Button
                            variant="light"
                            size="sm"
                            isIconOnly
                            onClick={() => handleBookmarkPost(post.id)}
                          >
                            {bookmarkedPosts.includes(post.id) ? (
                              <BookmarkSolidIcon className="w-4 h-4 text-primary" />
                            ) : (
                              <BookmarkIcon className="w-4 h-4" />
                            )}
                          </Button>
                        </Tooltip>

                        <Tooltip content="Visualizações">
                          <div className="flex items-center gap-1 text-xs text-default-500">
                            <EyeIcon className="w-4 h-4" />
                            <span>{Math.floor(Math.random() * 1000) + 100}</span>
                          </div>
                        </Tooltip>
                      </div>
                    </div>

                    {/* Comments Section */}
                    {expandedComments.includes(post.id) && (
                      <div className="mt-4 pt-4 border-t border-divider">
                        {/* Add Comment */}
                        {currentUser && (
                          <div className="flex gap-3 mb-4">
                            <Avatar
                              src={currentUser.photoURL || undefined}
                              name={currentUser.displayName || 'U'}
                              size="sm"
                            />
                            <div className="flex-1 flex gap-2">
                              <Input
                                placeholder="Escreva um comentário..."
                                value={newComment[post.id] || ''}
                                onChange={(e) => setNewComment(prev => ({
                                  ...prev,
                                  [post.id]: e.target.value
                                }))}
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    handleAddComment(post.id);
                                  }
                                }}
                                size="sm"
                              />
                              <Button
                                size="sm"
                                color="primary"
                                onClick={() => handleAddComment(post.id)}
                                isDisabled={!newComment[post.id]?.trim()}
                              >
                                Enviar
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Comments List */}
                        <div className="space-y-4">
                          {getTopLevelComments(post.id).map((comment) => (
                            <CommentComponent
                              key={comment.id}
                              comment={comment}
                              postId={post.id}
                              depth={0}
                            />
                          ))}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>
              ))}
            </div>

            {/* Load More / Loading */}
            <div className="flex justify-center mt-8">
              {loading ? (
                <div className="flex items-center gap-3">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="text-default-500">Carregando posts...</span>
                </div>
              ) : hasMore ? (
                <Button
                  color="primary"
                  variant="flat"
                  onClick={loadMorePosts}
                  size="lg"
                  className="px-8"
                >
                  Carregar Mais Posts
                </Button>
              ) : posts.length > 0 ? (
                <div className="text-center text-default-500">
                  <p>Você chegou ao fim do feed!</p>
                  <p className="text-sm mt-1">Que tal criar um novo post?</p>
                </div>
              ) : (
                <div className="text-center text-default-500">
                  <p>Nenhum post encontrado</p>
                  <p className="text-sm mt-1">Tente ajustar os filtros ou criar o primeiro post!</p>
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Trending & Discovery */}
          <div className="lg:col-span-1 space-y-6">
            {/* Trending Tags */}
            <Card className="sticky top-6">
              <CardHeader>
                <div className="flex items-center gap-2">
                  <FireIcon className="w-5 h-5 text-orange-500" />
                  <h3 className="font-semibold">Tags em Alta</h3>
                </div>
              </CardHeader>
              <CardBody className="space-y-3">
                {trendingTags.slice(0, 8).map((item, index) => (
                  <div
                    key={item.tag}
                    className="flex items-center justify-between p-2 rounded-lg hover:bg-default-100 cursor-pointer transition-colors"
                    onClick={() => setSearchTerm(`#${item.tag}`)}
                  >
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-bold text-orange-500">#{index + 1}</span>
                      <span className="font-medium">#{item.tag}</span>
                    </div>
                    <Chip size="sm" variant="flat" color="orange">
                      {item.count}
                    </Chip>
                  </div>
                ))}
              </CardBody>
            </Card>

            {/* Trending Posts */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <SparklesIcon className="w-5 h-5 text-purple-500" />
                  <h3 className="font-semibold">Posts em Alta</h3>
                </div>
              </CardHeader>
              <CardBody className="space-y-4">
                {trendingPosts.map((post) => (
                  <div key={post.id} className="p-3 rounded-lg border border-divider hover:bg-default-50 cursor-pointer transition-colors">
                    <div className="flex items-center gap-2 mb-2">
                      <Avatar
                        src={post.authorAvatar}
                        name={post.authorName}
                        size="sm"
                      />
                      <div>
                        <p className="font-semibold text-sm">{post.authorName}</p>
                        <p className="text-xs text-default-500">{formatTimeAgo(post.createdAt)}</p>
                      </div>
                    </div>
                    <p className="text-sm text-default-700 line-clamp-2 mb-2">
                      {post.content}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-default-500">
                      <span className="flex items-center gap-1">
                        <HeartIcon className="w-3 h-3" />
                        {post.likesCount}
                      </span>
                      <span className="flex items-center gap-1">
                        <ChatBubbleLeftIcon className="w-3 h-3" />
                        {post.commentsCount}
                      </span>
                      <span className="flex items-center gap-1">
                        <ShareIcon className="w-3 h-3" />
                        {post.sharesCount}
                      </span>
                    </div>
                  </div>
                ))}
              </CardBody>
            </Card>

            {/* Suggested Users */}
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <span className="text-blue-500">👥</span>
                  <h3 className="font-semibold">Usuários Sugeridos</h3>
                </div>
              </CardHeader>
              <CardBody className="space-y-4">
                {suggestedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar
                        src={user.avatar}
                        name={user.name}
                        size="sm"
                      />
                      <div>
                        <p className="font-semibold text-sm">{user.name}</p>
                        <p className="text-xs text-default-500">{user.bio}</p>
                        <p className="text-xs text-default-400">{user.followers} seguidores</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      color="primary"
                      variant={user.isFollowing ? "flat" : "solid"}
                      onClick={() => {
                        setSuggestedUsers(prev => prev.map(u =>
                          u.id === user.id ? { ...u, isFollowing: !u.isFollowing } : u
                        ));
                      }}
                    >
                      {user.isFollowing ? 'Seguindo' : 'Seguir'}
                    </Button>
                  </div>
                ))}
              </CardBody>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <h3 className="font-semibold">Estatísticas da Comunidade</h3>
              </CardHeader>
              <CardBody className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-default-600">Posts hoje</span>
                  <Badge color="primary" variant="flat">
                    {Math.floor(Math.random() * 50) + 20}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-default-600">Usuários ativos</span>
                  <Badge color="success" variant="flat">
                    {Math.floor(Math.random() * 200) + 100}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-default-600">Jogos discutidos</span>
                  <Badge color="secondary" variant="flat">
                    {Math.floor(Math.random() * 30) + 15}
                  </Badge>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>

        {/* Create Post Modal */}
        <Modal
          isOpen={isOpen}
          onClose={onClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                <span>Criar Post</span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar
                    src={currentUser?.photoURL || undefined}
                    name={currentUser?.displayName || 'U'}
                    size="md"
                  />
                  <div>
                    <p className="font-semibold">{currentUser?.displayName || 'Usuário'}</p>
                    <Select
                      selectedKeys={[newPost.visibility]}
                      onSelectionChange={(keys) => setNewPost(prev => ({
                        ...prev,
                        visibility: Array.from(keys)[0] as string
                      }))}
                      size="sm"
                      className="w-32"
                    >
                      <SelectItem key="public">Público</SelectItem>
                      <SelectItem key="followers">Seguidores</SelectItem>
                      <SelectItem key="friends">Amigos</SelectItem>
                    </Select>
                  </div>
                </div>

                <Tabs
                  selectedKey={newPost.type}
                  onSelectionChange={(key) => setNewPost(prev => ({
                    ...prev,
                    type: key as 'text' | 'image' | 'video' | 'link' | 'achievement' | 'review'
                  }))}
                  variant="underlined"
                >
                  <Tab
                    key="text"
                    title={
                      <div className="flex items-center gap-2">
                        <DocumentTextIcon className="w-4 h-4" />
                        <span>Texto</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Textarea
                        placeholder="O que você está jogando hoje? Compartilhe sua experiência..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={4}
                        maxRows={8}
                        maxLength={1000}
                      />
                    </div>
                  </Tab>

                  <Tab
                    key="image"
                    title={
                      <div className="flex items-center gap-2">
                        <PhotoIcon className="w-4 h-4" />
                        <span>Imagem</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Textarea
                        placeholder="Descreva sua imagem..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={3}
                        maxRows={6}
                        maxLength={1000}
                      />

                      {/* File Upload Area */}
                      <div
                        className="border-2 border-dashed border-default-300 rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <PhotoIcon className="w-12 h-12 mx-auto text-default-400 mb-2" />
                        <p className="text-default-500">
                          {uploadingMedia ? 'Enviando...' : 'Clique para adicionar imagens'}
                        </p>
                        <p className="text-xs text-default-400 mt-1">PNG, JPG até 10MB cada</p>
                        {uploadingMedia && <Progress size="sm" isIndeterminate className="mt-2" />}
                      </div>

                      <input
                        ref={fileInputRef}
                        type="file"
                        multiple
                        accept="image/*"
                        className="hidden"
                        onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                      />

                      {/* Media Preview */}
                      {mediaPreview.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                          {mediaPreview.map((url, index) => (
                            <div key={index} className="relative">
                              <Image
                                src={url}
                                alt={`Preview ${index + 1}`}
                                className="w-full h-32 object-cover rounded-lg"
                              />
                              <Button
                                isIconOnly
                                size="sm"
                                color="danger"
                                variant="solid"
                                className="absolute top-2 right-2"
                                onClick={() => removeMediaPreview(index)}
                              >
                                <XMarkIcon className="w-4 h-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </Tab>

                  <Tab
                    key="video"
                    title={
                      <div className="flex items-center gap-2">
                        <VideoCameraIcon className="w-4 h-4" />
                        <span>Vídeo</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Textarea
                        placeholder="Adicione uma descrição para seu vídeo..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={3}
                        maxRows={6}
                        maxLength={1000}
                      />
                      <Input
                        label="URL do Vídeo"
                        placeholder="https://youtube.com/watch?v=... ou https://twitch.tv/..."
                        value={newPost.videoUrl}
                        onChange={(e) => setNewPost(prev => ({ ...prev, videoUrl: e.target.value }))}
                        startContent={<PlayIcon className="w-4 h-4" />}
                      />
                    </div>
                  </Tab>

                  <Tab
                    key="link"
                    title={
                      <div className="flex items-center gap-2">
                        <LinkIcon className="w-4 h-4" />
                        <span>Link</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Input
                        label="URL do Link"
                        placeholder="https://exemplo.com"
                        value={newPost.linkUrl}
                        onChange={(e) => {
                          const url = e.target.value;
                          setNewPost(prev => ({ ...prev, linkUrl: url }));
                          if (url) {
                            fetchLinkPreview(url);
                          }
                        }}
                        startContent={<LinkIcon className="w-4 h-4" />}
                      />

                      {/* Link Preview */}
                      {loadingLinkPreview && (
                        <div className="p-4 border rounded-lg">
                          <Skeleton className="h-4 w-3/4 mb-2" />
                          <Skeleton className="h-3 w-full mb-1" />
                          <Skeleton className="h-3 w-2/3" />
                        </div>
                      )}

                      {linkPreview && (
                        <Card className="p-4">
                          <div className="flex gap-4">
                            <Image
                              src={linkPreview.image}
                              alt="Link preview"
                              className="w-20 h-20 object-cover rounded"
                            />
                            <div className="flex-1">
                              <h4 className="font-semibold text-sm">{linkPreview.title}</h4>
                              <p className="text-xs text-default-500 mt-1">{linkPreview.description}</p>
                              <p className="text-xs text-primary mt-2">{linkPreview.url}</p>
                            </div>
                          </div>
                        </Card>
                      )}

                      <Textarea
                        placeholder="Adicione um comentário sobre o link..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={3}
                        maxRows={6}
                        maxLength={1000}
                      />
                    </div>
                  </Tab>

                  <Tab
                    key="achievement"
                    title={
                      <div className="flex items-center gap-2">
                        <TrophyIcon className="w-4 h-4" />
                        <span>Conquista</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Input
                        label="Nome da Conquista"
                        placeholder="Ex: Platina no Elden Ring"
                        value={newPost.achievementTitle}
                        onChange={(e) => setNewPost(prev => ({ ...prev, achievementTitle: e.target.value }))}
                        startContent={<TrophyIcon className="w-4 h-4" />}
                      />
                      <Textarea
                        placeholder="Conte como foi conseguir essa conquista..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={4}
                        maxRows={8}
                        maxLength={1000}
                      />
                      <Input
                        label="Jogo"
                        placeholder="Nome do jogo"
                        value={newPost.gameName}
                        onChange={(e) => setNewPost(prev => ({ ...prev, gameName: e.target.value }))}
                      />
                    </div>
                  </Tab>

                  <Tab
                    key="review"
                    title={
                      <div className="flex items-center gap-2">
                        <StarIcon className="w-4 h-4" />
                        <span>Review</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Input
                        label="Jogo"
                        placeholder="Nome do jogo que você está avaliando"
                        value={newPost.gameName}
                        onChange={(e) => setNewPost(prev => ({ ...prev, gameName: e.target.value }))}
                      />

                      <div>
                        <label className="text-sm font-medium mb-2 block">Sua Avaliação</label>
                        <div className="flex items-center gap-2">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Button
                              key={star}
                              isIconOnly
                              variant="light"
                              size="sm"
                              onClick={() => setNewPost(prev => ({ ...prev, reviewScore: star }))}
                            >
                              <StarIcon
                                className={`w-6 h-6 ${
                                  star <= newPost.reviewScore
                                    ? 'text-yellow-500 fill-current'
                                    : 'text-default-300'
                                }`}
                              />
                            </Button>
                          ))}
                          <span className="text-sm text-default-500 ml-2">
                            {newPost.reviewScore > 0 ? `${newPost.reviewScore}/5` : 'Não avaliado'}
                          </span>
                        </div>
                      </div>

                      <Textarea
                        placeholder="Escreva sua review detalhada do jogo..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={5}
                        maxRows={10}
                        maxLength={2000}
                      />
                    </div>
                  </Tab>
                </Tabs>

                <div className="grid grid-cols-2 gap-4">
                  {newPost.type !== 'achievement' && newPost.type !== 'review' && (
                    <Input
                      label="Jogo (opcional)"
                      placeholder="Ex: Valorant, Elden Ring"
                      value={newPost.gameName}
                      onChange={(e) => setNewPost(prev => ({
                        ...prev,
                        gameName: e.target.value,
                        gameId: e.target.value.toLowerCase().replace(/\s+/g, '-')
                      }))}
                    />
                  )}
                  <Input
                    label="Tags"
                    placeholder="Separe por vírgula: fps, competitivo"
                    value={newPost.tags}
                    onChange={(e) => setNewPost(prev => ({ ...prev, tags: e.target.value }))}
                    className={newPost.type === 'achievement' || newPost.type === 'review' ? 'col-span-2' : ''}
                  />
                </div>

                <div className="flex items-center justify-between text-sm text-default-500">
                  <span>{newPost.content.length}/1000 caracteres</span>
                  <div className="flex items-center gap-2">
                    <Button
                      isIconOnly
                      variant="light"
                      size="sm"
                    >
                      <FaceSmileIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                onPress={handleCreatePost}
                isDisabled={!newPost.content.trim()}
              >
                Publicar
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Share Post Modal */}
        <Modal
          isOpen={isShareOpen}
          onClose={onShareClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <ShareIcon className="w-5 h-5" />
                <span>Compartilhar Post</span>
              </div>
            </ModalHeader>
            <ModalBody>
              {sharePost && (
                <div className="space-y-4">
                  {/* Original Post Preview */}
                  <div className="border border-divider rounded-lg p-4 bg-default-50">
                    <div className="flex items-center gap-3 mb-3">
                      <Avatar
                        src={sharePost.authorAvatar}
                        name={sharePost.authorName}
                        size="sm"
                      />
                      <div>
                        <p className="font-semibold text-sm">{sharePost.authorName}</p>
                        <p className="text-xs text-default-500">
                          {formatTimeAgo(sharePost.createdAt)}
                        </p>
                      </div>
                    </div>
                    <p className="text-sm text-default-700 mb-3">
                      {sharePost.content}
                    </p>
                    {sharePost.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {sharePost.tags.map((tag, index) => (
                          <Chip key={index} size="sm" variant="flat" color="secondary">
                            #{tag}
                          </Chip>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Share Comment */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Adicione um comentário (opcional)
                    </label>
                    <Textarea
                      placeholder="O que você pensa sobre este post?"
                      value={shareComment}
                      onChange={(e) => setShareComment(e.target.value)}
                      minRows={3}
                      maxRows={6}
                      maxLength={500}
                    />
                    <div className="flex justify-between items-center text-xs text-default-500">
                      <span>{shareComment.length}/500 caracteres</span>
                    </div>
                  </div>

                  {/* Share Options */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-sm">Opções de Compartilhamento</h4>
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        variant="flat"
                        startContent={<ArrowPathIcon className="w-4 h-4" />}
                        onClick={() => handleSharePost(sharePost.id, shareComment)}
                        fullWidth
                      >
                        Repostar no Feed
                      </Button>
                      <Button
                        variant="flat"
                        startContent={<span className="text-blue-500">🐦</span>}
                        onClick={() => handleExternalShare(sharePost, 'twitter')}
                        fullWidth
                      >
                        Twitter
                      </Button>
                      <Button
                        variant="flat"
                        startContent={<span className="text-blue-600">📘</span>}
                        onClick={() => handleExternalShare(sharePost, 'facebook')}
                        fullWidth
                      >
                        Facebook
                      </Button>
                      <Button
                        variant="flat"
                        startContent={<span className="text-green-500">💬</span>}
                        onClick={() => handleExternalShare(sharePost, 'whatsapp')}
                        fullWidth
                      >
                        WhatsApp
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="flat" onPress={onShareClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                startContent={<DocumentTextIcon className="w-4 h-4" />}
                onClick={() => sharePost && handleExternalShare(sharePost, 'copy')}
              >
                Copiar Link
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>

        {/* Report Modal */}
        <Modal
          isOpen={showReportModal}
          onClose={() => setShowReportModal(false)}
          size="lg"
        >
          <ModalContent>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <span className="text-red-500">⚠️</span>
                <span>Reportar Conteúdo</span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-default-600 mb-4">
                    Ajude-nos a manter a comunidade segura reportando conteúdo inadequado.
                    Sua denúncia será analisada pela nossa equipe de moderação.
                  </p>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Motivo do Report *
                  </label>
                  <Select
                    placeholder="Selecione o motivo"
                    selectedKeys={reportReason ? [reportReason] : []}
                    onSelectionChange={(keys) => setReportReason(Array.from(keys)[0] as string)}
                  >
                    {REPORT_REASONS.map((reason) => (
                      <SelectItem key={reason} value={reason}>
                        {reason}
                      </SelectItem>
                    ))}
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block">
                    Descrição Adicional (opcional)
                  </label>
                  <Textarea
                    placeholder="Forneça mais detalhes sobre o problema..."
                    value={reportDescription}
                    onChange={(e) => setReportDescription(e.target.value)}
                    minRows={3}
                    maxRows={6}
                    maxLength={500}
                  />
                  <div className="flex justify-end mt-1">
                    <span className="text-xs text-default-500">
                      {reportDescription.length}/500
                    </span>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-start gap-2">
                    <span className="text-yellow-600">💡</span>
                    <div className="text-sm text-yellow-800">
                      <p className="font-medium mb-1">Dica:</p>
                      <p>
                        Reports falsos ou abusivos podem resultar em ações contra sua conta.
                        Use esta função apenas para conteúdo que realmente viola nossas diretrizes.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button
                variant="flat"
                onPress={() => setShowReportModal(false)}
              >
                Cancelar
              </Button>
              <Button
                color="danger"
                onPress={handleReport}
                isDisabled={!reportReason.trim()}
                startContent={<span>⚠️</span>}
              >
                Enviar Report
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}
