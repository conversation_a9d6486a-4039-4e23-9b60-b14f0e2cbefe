import { useState, useEffect } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Divider,
  Input,
  Textarea,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Image,
  Tabs,
  Tab,
  Select,
  SelectItem,
} from '@heroui/react';
import {
  PlusIcon,
  HeartIcon,
  ShareIcon,
  ChatBubbleLeftIcon,
  EllipsisHorizontalIcon,
  PhotoIcon,
  VideoCameraIcon,
  LinkIcon,
  FaceSmileIcon,
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  FireIcon,
  ClockIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';
import {
  HeartIcon as HeartSolidIcon,
} from '@heroicons/react/24/solid';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { useAuth } from '@/contexts/AuthContext';
import { Post, Comment } from '@/types/community';
import { PostsService } from '@/services/communityService';

// Mock data for demonstration
const mockPosts: Post[] = [
  {
    id: '1',
    authorId: 'user1',
    authorName: 'GamerPro2024',
    authorAvatar: 'https://i.pravatar.cc/150?u=user1',
    content: 'Acabei de terminar Elden Ring pela terceira vez! Que jogo incrível. A cada playthrough descubro algo novo. Alguém mais viciado nesse mundo? 🎮✨',
    type: 'text',
    gameId: 'elden-ring',
    gameName: 'Elden Ring',
    tags: ['elden-ring', 'souls-like', 'rpg'],
    likesCount: 124,
    commentsCount: 18,
    sharesCount: 7,
    createdAt: { seconds: Date.now() / 1000 - 3600 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 3600 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '2',
    authorId: 'user2',
    authorName: 'StreamerQueen',
    authorAvatar: 'https://i.pravatar.cc/150?u=user2',
    content: 'Live hoje às 20h jogando Valorant! Vamos subir de rank juntos? Drop no chat quem vai aparecer! 🔥',
    type: 'text',
    gameId: 'valorant',
    gameName: 'Valorant',
    tags: ['valorant', 'stream', 'live'],
    likesCount: 89,
    commentsCount: 23,
    sharesCount: 12,
    createdAt: { seconds: Date.now() / 1000 - 7200 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 7200 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '3',
    authorId: 'user3',
    authorName: 'RetroGamer90',
    authorAvatar: 'https://i.pravatar.cc/150?u=user3',
    content: 'Encontrei essa gem escondida na Steam! Hollow Knight continua sendo uma obra-prima. Se você curte metroidvania, não pode perder.',
    type: 'image',
    gameId: 'hollow-knight',
    gameName: 'Hollow Knight',
    images: ['/api/placeholder/600/400'],
    tags: ['hollow-knight', 'metroidvania', 'indie'],
    likesCount: 67,
    commentsCount: 15,
    sharesCount: 8,
    createdAt: { seconds: Date.now() / 1000 - 14400 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 14400 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '4',
    authorId: 'user4',
    authorName: 'CompetitivePlayer',
    authorAvatar: 'https://i.pravatar.cc/150?u=user4',
    content: 'Finalmente cheguei ao Diamante no Valorant! 💎 Foi uma jornada longa, mas valeu cada partida. Dicas para quem está subindo: comunicação é TUDO!',
    type: 'text',
    gameId: 'valorant',
    gameName: 'Valorant',
    tags: ['valorant', 'competitive', 'rank', 'diamante'],
    likesCount: 156,
    commentsCount: 34,
    sharesCount: 19,
    createdAt: { seconds: Date.now() / 1000 - 21600 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 21600 } as any,
    isEdited: false,
    visibility: 'public',
  },
  {
    id: '5',
    authorId: 'user5',
    authorName: 'IndieExplorer',
    authorAvatar: 'https://i.pravatar.cc/150?u=user5',
    content: 'Lista dos melhores jogos indie de 2024 que vocês PRECISAM jogar! Cada um é uma experiência única. Qual vocês já jogaram?',
    type: 'link',
    linkUrl: 'https://example.com/best-indie-games-2024',
    linkTitle: 'Os 10 Melhores Jogos Indie de 2024',
    linkDescription: 'Uma curadoria especial dos jogos independentes que marcaram o ano',
    tags: ['indie', 'games', '2024', 'recomendacao'],
    likesCount: 203,
    commentsCount: 45,
    sharesCount: 28,
    createdAt: { seconds: Date.now() / 1000 - 28800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 28800 } as any,
    isEdited: false,
    visibility: 'public',
  },
];

const mockComments: Comment[] = [
  {
    id: 'c1',
    postId: '1',
    authorId: 'user6',
    authorName: 'SoulsVeteran',
    authorAvatar: 'https://i.pravatar.cc/150?u=user6',
    content: 'Elden Ring é realmente viciante! Estou na minha segunda run e ainda descobrindo segredos.',
    likesCount: 12,
    repliesCount: 2,
    createdAt: { seconds: Date.now() / 1000 - 1800 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 1800 } as any,
    isEdited: false,
  },
  {
    id: 'c2',
    postId: '1',
    authorId: 'user7',
    authorName: 'MagicUser',
    authorAvatar: 'https://i.pravatar.cc/150?u=user7',
    content: 'Qual build vocês recomendam para iniciantes? Estou começando agora.',
    likesCount: 8,
    repliesCount: 5,
    createdAt: { seconds: Date.now() / 1000 - 900 } as any,
    updatedAt: { seconds: Date.now() / 1000 - 900 } as any,
    isEdited: false,
  },
];

export default function FeedPage() {
  const { currentUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [posts, setPosts] = useState<Post[]>(mockPosts);
  const [comments, setComments] = useState<Comment[]>(mockComments);
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedSort, setSelectedSort] = useState('recent');
  const [searchTerm, setSearchTerm] = useState('');
  const [likedPosts, setLikedPosts] = useState<string[]>([]);
  const [expandedComments, setExpandedComments] = useState<string[]>([]);
  const [newComment, setNewComment] = useState<{ [key: string]: string }>({});
  
  // New post form
  const [newPost, setNewPost] = useState({
    content: '',
    type: 'text' as 'text' | 'image' | 'link',
    gameId: '',
    tags: '',
    linkUrl: '',
    linkTitle: '',
    visibility: 'public',
  });

  useEffect(() => {
    loadFeed();
  }, [selectedFilter, selectedSort]);

  const loadFeed = async () => {
    // In production, load real data based on filters
    // For now, using mock data with filtering
    let filteredPosts = [...mockPosts];
    
    if (selectedFilter !== 'all') {
      filteredPosts = filteredPosts.filter(post => 
        post.gameId === selectedFilter || 
        post.tags.includes(selectedFilter)
      );
    }
    
    if (searchTerm) {
      filteredPosts = filteredPosts.filter(post =>
        post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.authorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // Sort posts
    filteredPosts.sort((a, b) => {
      switch (selectedSort) {
        case 'popular':
          return (b.likesCount + b.commentsCount) - (a.likesCount + a.commentsCount);
        case 'recent':
        default:
          return b.createdAt.seconds - a.createdAt.seconds;
      }
    });
    
    setPosts(filteredPosts);
  };

  const handleCreatePost = async () => {
    if (!currentUser || !newPost.content.trim()) return;

    const post: Post = {
      id: `post-${Date.now()}`,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newPost.content,
      type: newPost.type,
      gameId: newPost.gameId,
      gameName: newPost.gameId ? `Game ${newPost.gameId}` : undefined,
      linkUrl: newPost.linkUrl || undefined,
      linkTitle: newPost.linkTitle || undefined,
      tags: newPost.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
      likesCount: 0,
      commentsCount: 0,
      sharesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
      visibility: newPost.visibility as 'public' | 'followers' | 'friends',
    };

    setPosts(prev => [post, ...prev]);
    setNewPost({
      content: '',
      type: 'text',
      gameId: '',
      tags: '',
      linkUrl: '',
      linkTitle: '',
      visibility: 'public',
    });
    onClose();
  };

  const handleLikePost = (postId: string) => {
    if (!currentUser) return;

    const isLiked = likedPosts.includes(postId);
    
    if (isLiked) {
      setLikedPosts(prev => prev.filter(id => id !== postId));
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, likesCount: post.likesCount - 1 }
          : post
      ));
    } else {
      setLikedPosts(prev => [...prev, postId]);
      setPosts(prev => prev.map(post => 
        post.id === postId 
          ? { ...post, likesCount: post.likesCount + 1 }
          : post
      ));
    }
  };

  const handleAddComment = (postId: string) => {
    if (!currentUser || !newComment[postId]?.trim()) return;

    const comment: Comment = {
      id: `comment-${Date.now()}`,
      postId,
      authorId: currentUser.uid,
      authorName: currentUser.displayName || 'Usuário',
      authorAvatar: currentUser.photoURL || `https://i.pravatar.cc/150?u=${currentUser.uid}`,
      content: newComment[postId],
      likesCount: 0,
      repliesCount: 0,
      createdAt: { seconds: Date.now() / 1000 } as any,
      updatedAt: { seconds: Date.now() / 1000 } as any,
      isEdited: false,
    };

    setComments(prev => [...prev, comment]);
    setPosts(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, commentsCount: post.commentsCount + 1 }
        : post
    ));
    setNewComment(prev => ({ ...prev, [postId]: '' }));
  };

  const toggleComments = (postId: string) => {
    setExpandedComments(prev => 
      prev.includes(postId) 
        ? prev.filter(id => id !== postId)
        : [...prev, postId]
    );
  };

  const formatTimeAgo = (timestamp: any) => {
    const now = Date.now();
    const postTime = timestamp.seconds * 1000;
    const diff = now - postTime;
    
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (days > 0) return `${days}d atrás`;
    if (hours > 0) return `${hours}h atrás`;
    if (minutes > 0) return `${minutes}m atrás`;
    return 'Agora';
  };

  const getPostComments = (postId: string) => {
    return comments.filter(comment => comment.postId === postId);
  };

  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <FireIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Feed da Comunidade
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Feed{" "}
              <span className={title({ color: "violet", size: "lg" })}>Social</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Acompanhe as últimas novidades, compartilhe suas experiências
              e conecte-se com outros gamers da comunidade.
            </p>
          </div>
        </section>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Filters */}
          <div className="lg:col-span-1">
            <Card className="sticky top-6">
              <CardHeader>
                <h3 className="font-semibold">Filtros</h3>
              </CardHeader>
              <CardBody className="space-y-4">
                <div>
                  <label className="text-sm font-medium mb-2 block">Buscar</label>
                  <Input
                    placeholder="Buscar posts..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    startContent={<MagnifyingGlassIcon className="w-4 h-4" />}
                    size="sm"
                  />
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Categoria</label>
                  <Select
                    placeholder="Todas as categorias"
                    selectedKeys={[selectedFilter]}
                    onSelectionChange={(keys) => setSelectedFilter(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="all">Todas</SelectItem>
                    <SelectItem key="valorant">Valorant</SelectItem>
                    <SelectItem key="elden-ring">Elden Ring</SelectItem>
                    <SelectItem key="hollow-knight">Hollow Knight</SelectItem>
                    <SelectItem key="indie">Jogos Indie</SelectItem>
                    <SelectItem key="competitive">Competitivo</SelectItem>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium mb-2 block">Ordenar por</label>
                  <Select
                    selectedKeys={[selectedSort]}
                    onSelectionChange={(keys) => setSelectedSort(Array.from(keys)[0] as string)}
                    size="sm"
                  >
                    <SelectItem key="recent">Mais recentes</SelectItem>
                    <SelectItem key="popular">Mais populares</SelectItem>
                  </Select>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Main Feed */}
          <div className="lg:col-span-3 space-y-6">
            {/* Create Post */}
            {currentUser && (
              <Card>
                <CardBody className="p-4">
                  <div className="flex items-center gap-4">
                    <Avatar
                      src={currentUser.photoURL || undefined}
                      name={currentUser.displayName || 'U'}
                      size="md"
                    />
                    <Button
                      variant="flat"
                      className="flex-1 justify-start text-default-500"
                      onClick={onOpen}
                    >
                      O que você está jogando hoje?
                    </Button>
                    <Button
                      color="primary"
                      startContent={<PlusIcon className="w-4 h-4" />}
                      onClick={onOpen}
                    >
                      Criar Post
                    </Button>
                  </div>
                </CardBody>
              </Card>
            )}

            {/* Posts Feed */}
            <div className="space-y-6">
              {posts.map((post) => (
                <Card key={post.id} className="w-full">
                  <CardBody className="p-6">
                    {/* Post Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <Avatar
                          src={post.authorAvatar}
                          name={post.authorName}
                          size="md"
                        />
                        <div>
                          <p className="font-semibold">{post.authorName}</p>
                          <div className="flex items-center gap-2 text-sm text-default-500">
                            <span>{formatTimeAgo(post.createdAt)}</span>
                            {post.gameName && (
                              <>
                                <span>•</span>
                                <Chip size="sm" color="primary" variant="flat">
                                  {post.gameName}
                                </Chip>
                              </>
                            )}
                            {post.isEdited && (
                              <>
                                <span>•</span>
                                <span className="text-xs">editado</span>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <Dropdown>
                        <DropdownTrigger>
                          <Button
                            isIconOnly
                            variant="light"
                            size="sm"
                          >
                            <EllipsisHorizontalIcon className="w-4 h-4" />
                          </Button>
                        </DropdownTrigger>
                        <DropdownMenu>
                          <DropdownItem>Compartilhar</DropdownItem>
                          <DropdownItem>Salvar</DropdownItem>
                          <DropdownItem>Reportar</DropdownItem>
                        </DropdownMenu>
                      </Dropdown>
                    </div>

                    {/* Post Content */}
                    <div className="mb-4">
                      <p className="text-default-700 leading-relaxed mb-3">
                        {post.content}
                      </p>
                      
                      {/* Post Media */}
                      {post.type === 'image' && post.images && (
                        <div className="mb-3">
                          <Image
                            src={post.images[0]}
                            alt="Post image"
                            className="w-full max-h-96 object-cover rounded-lg"
                          />
                        </div>
                      )}
                      
                      {post.type === 'link' && post.linkUrl && (
                        <Card className="mb-3 border">
                          <CardBody className="p-4">
                            <div className="flex items-start gap-3">
                              <LinkIcon className="w-5 h-5 text-primary flex-shrink-0 mt-1" />
                              <div>
                                <h4 className="font-semibold text-sm mb-1">
                                  {post.linkTitle || post.linkUrl}
                                </h4>
                                {post.linkDescription && (
                                  <p className="text-sm text-default-600 mb-2">
                                    {post.linkDescription}
                                  </p>
                                )}
                                <p className="text-xs text-default-500 truncate">
                                  {post.linkUrl}
                                </p>
                              </div>
                            </div>
                          </CardBody>
                        </Card>
                      )}
                    </div>

                    {/* Post Tags */}
                    {post.tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map((tag) => (
                          <Chip
                            key={tag}
                            size="sm"
                            variant="flat"
                            color="secondary"
                            className="text-xs cursor-pointer hover:bg-secondary/20"
                            onClick={() => setSearchTerm(tag)}
                          >
                            #{tag}
                          </Chip>
                        ))}
                      </div>
                    )}

                    <Divider className="my-4" />

                    {/* Post Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-6">
                        <Button
                          variant="light"
                          size="sm"
                          startContent={
                            likedPosts.includes(post.id) ? (
                              <HeartSolidIcon className="w-4 h-4 text-red-500" />
                            ) : (
                              <HeartIcon className="w-4 h-4" />
                            )
                          }
                          onClick={() => handleLikePost(post.id)}
                          className={likedPosts.includes(post.id) ? 'text-red-500' : ''}
                        >
                          {post.likesCount}
                        </Button>
                        <Button
                          variant="light"
                          size="sm"
                          startContent={<ChatBubbleLeftIcon className="w-4 h-4" />}
                          onClick={() => toggleComments(post.id)}
                        >
                          {post.commentsCount}
                        </Button>
                        <Button
                          variant="light"
                          size="sm"
                          startContent={<ShareIcon className="w-4 h-4" />}
                        >
                          {post.sharesCount}
                        </Button>
                      </div>
                    </div>

                    {/* Comments Section */}
                    {expandedComments.includes(post.id) && (
                      <div className="mt-4 pt-4 border-t border-divider">
                        {/* Add Comment */}
                        {currentUser && (
                          <div className="flex gap-3 mb-4">
                            <Avatar
                              src={currentUser.photoURL || undefined}
                              name={currentUser.displayName || 'U'}
                              size="sm"
                            />
                            <div className="flex-1 flex gap-2">
                              <Input
                                placeholder="Escreva um comentário..."
                                value={newComment[post.id] || ''}
                                onChange={(e) => setNewComment(prev => ({
                                  ...prev,
                                  [post.id]: e.target.value
                                }))}
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    handleAddComment(post.id);
                                  }
                                }}
                                size="sm"
                              />
                              <Button
                                size="sm"
                                color="primary"
                                onClick={() => handleAddComment(post.id)}
                                isDisabled={!newComment[post.id]?.trim()}
                              >
                                Enviar
                              </Button>
                            </div>
                          </div>
                        )}

                        {/* Comments List */}
                        <div className="space-y-3">
                          {getPostComments(post.id).map((comment) => (
                            <div key={comment.id} className="flex gap-3">
                              <Avatar
                                src={comment.authorAvatar}
                                name={comment.authorName}
                                size="sm"
                              />
                              <div className="flex-1">
                                <div className="bg-default-100 rounded-lg p-3">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="font-semibold text-sm">
                                      {comment.authorName}
                                    </span>
                                    <span className="text-xs text-default-500">
                                      {formatTimeAgo(comment.createdAt)}
                                    </span>
                                  </div>
                                  <p className="text-sm">{comment.content}</p>
                                </div>
                                <div className="flex items-center gap-4 mt-2 text-xs">
                                  <Button
                                    variant="light"
                                    size="sm"
                                    className="text-xs h-6 min-w-0 px-2"
                                  >
                                    Curtir ({comment.likesCount})
                                  </Button>
                                  <Button
                                    variant="light"
                                    size="sm"
                                    className="text-xs h-6 min-w-0 px-2"
                                  >
                                    Responder
                                  </Button>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Create Post Modal */}
        <Modal
          isOpen={isOpen}
          onClose={onClose}
          size="2xl"
          scrollBehavior="inside"
        >
          <ModalContent>
            <ModalHeader>
              <div className="flex items-center gap-2">
                <SparklesIcon className="w-5 h-5" />
                <span>Criar Post</span>
              </div>
            </ModalHeader>
            <ModalBody>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Avatar
                    src={currentUser?.photoURL || undefined}
                    name={currentUser?.displayName || 'U'}
                    size="md"
                  />
                  <div>
                    <p className="font-semibold">{currentUser?.displayName || 'Usuário'}</p>
                    <Select
                      selectedKeys={[newPost.visibility]}
                      onSelectionChange={(keys) => setNewPost(prev => ({
                        ...prev,
                        visibility: Array.from(keys)[0] as string
                      }))}
                      size="sm"
                      className="w-32"
                    >
                      <SelectItem key="public">Público</SelectItem>
                      <SelectItem key="followers">Seguidores</SelectItem>
                      <SelectItem key="friends">Amigos</SelectItem>
                    </Select>
                  </div>
                </div>

                <Tabs
                  selectedKey={newPost.type}
                  onSelectionChange={(key) => setNewPost(prev => ({
                    ...prev,
                    type: key as 'text' | 'image' | 'link'
                  }))}
                  variant="underlined"
                >
                  <Tab
                    key="text"
                    title={
                      <div className="flex items-center gap-2">
                        <span>📝</span>
                        <span>Texto</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Textarea
                        placeholder="O que você está jogando hoje? Compartilhe sua experiência..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={4}
                        maxRows={8}
                        maxLength={1000}
                      />
                    </div>
                  </Tab>

                  <Tab
                    key="image"
                    title={
                      <div className="flex items-center gap-2">
                        <PhotoIcon className="w-4 h-4" />
                        <span>Imagem</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Textarea
                        placeholder="Descreva sua imagem..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={3}
                        maxRows={6}
                        maxLength={1000}
                      />
                      <div className="border-2 border-dashed border-default-300 rounded-lg p-8 text-center">
                        <PhotoIcon className="w-12 h-12 mx-auto text-default-400 mb-2" />
                        <p className="text-default-500">Clique para adicionar uma imagem</p>
                        <p className="text-xs text-default-400 mt-1">PNG, JPG até 10MB</p>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="link"
                    title={
                      <div className="flex items-center gap-2">
                        <LinkIcon className="w-4 h-4" />
                        <span>Link</span>
                      </div>
                    }
                  >
                    <div className="space-y-4">
                      <Input
                        label="URL do Link"
                        placeholder="https://exemplo.com"
                        value={newPost.linkUrl}
                        onChange={(e) => setNewPost(prev => ({ ...prev, linkUrl: e.target.value }))}
                      />
                      <Input
                        label="Título do Link"
                        placeholder="Título que aparecerá no card"
                        value={newPost.linkTitle}
                        onChange={(e) => setNewPost(prev => ({ ...prev, linkTitle: e.target.value }))}
                      />
                      <Textarea
                        placeholder="Adicione um comentário sobre o link..."
                        value={newPost.content}
                        onChange={(e) => setNewPost(prev => ({ ...prev, content: e.target.value }))}
                        minRows={3}
                        maxRows={6}
                        maxLength={1000}
                      />
                    </div>
                  </Tab>
                </Tabs>

                <div className="grid grid-cols-2 gap-4">
                  <Input
                    label="Jogo (opcional)"
                    placeholder="Ex: Valorant, Elden Ring"
                    value={newPost.gameId}
                    onChange={(e) => setNewPost(prev => ({ ...prev, gameId: e.target.value }))}
                  />
                  <Input
                    label="Tags"
                    placeholder="Separe por vírgula: fps, competitivo"
                    value={newPost.tags}
                    onChange={(e) => setNewPost(prev => ({ ...prev, tags: e.target.value }))}
                  />
                </div>

                <div className="flex items-center justify-between text-sm text-default-500">
                  <span>{newPost.content.length}/1000 caracteres</span>
                  <div className="flex items-center gap-2">
                    <Button
                      isIconOnly
                      variant="light"
                      size="sm"
                    >
                      <FaceSmileIcon className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button variant="flat" onPress={onClose}>
                Cancelar
              </Button>
              <Button
                color="primary"
                onPress={handleCreatePost}
                isDisabled={!newPost.content.trim()}
              >
                Publicar
              </Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </div>
    </DefaultLayout>
  );
}
