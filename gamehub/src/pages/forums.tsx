import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  Chip,
} from '@heroui/react';
import {
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { ForumSystem } from '@/components/community';

export default function ForumsPage() {
  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <ChatBubbleLeftRightIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Fóruns da Comunidade
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Fóruns de{" "}
              <span className={title({ color: "violet", size: "lg" })}>Discussão</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Participe de discussões organizadas sobre seus jogos favoritos,
              compartilhe dicas e tire dúvidas com a comunidade.
            </p>
          </div>
        </section>

        {/* Forum System */}
        <ForumSystem />
      </div>
    </DefaultLayout>
  );
}
