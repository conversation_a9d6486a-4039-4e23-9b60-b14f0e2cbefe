import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardB<PERSON>,
  CardHeader,
  Chip,
} from '@heroui/react';
import {
  TrophyIcon,
} from '@heroicons/react/24/outline';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { AchievementSystem } from '@/components/community';

export default function AchievementsPage() {
  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <TrophyIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Sistema de Conquistas
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Conquistas e{" "}
              <span className={title({ color: "violet", size: "lg" })}>Rankings</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Desbloqueie conquistas, ganhe badges exclusivos e compete
              nos rankings da comunidade para mostrar seu progresso.
            </p>
          </div>
        </section>

        {/* Achievement System */}
        <AchievementSystem />
      </div>
    </DefaultLayout>
  );
}
