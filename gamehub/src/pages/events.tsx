import { useState, useEffect } from 'react';
import {
  <PERSON>,
  CardBody,
  CardHeader,
  Chip,
} from '@heroui/react';
import {
  CalendarDaysIcon,
} from '@heroicons/react/24/outline';

import DefaultLayout from '@/layouts/default';
import { title, subtitle } from '@/components/primitives';
import { GroupsAndEvents } from '@/components/community';

export default function EventsPage() {
  return (
    <DefaultLayout>
      <div className="flex flex-col gap-6 py-8 md:py-10">
        {/* Header */}
        <section className="flex flex-col items-center justify-center gap-4 py-8 md:py-10">
          <div className="text-center max-w-4xl">
            <div className="flex items-center justify-center gap-2 mb-4">
              <CalendarDaysIcon className="w-8 h-8 text-primary" />
              <Chip color="primary" variant="flat" size="sm">
                Eventos da Comunidade
              </Chip>
            </div>
            
            <h1 className={title({ size: "lg" })}>
              Eventos e{" "}
              <span className={title({ color: "violet", size: "lg" })}>Torneios</span>
            </h1>
            
            <p className={subtitle({ class: "mt-4 max-w-2xl mx-auto" })}>
              Descubra e participe de eventos incríveis, torneios competitivos,
              meetups e muito mais organizados pela comunidade.
            </p>
          </div>
        </section>

        {/* Events System */}
        <GroupsAndEvents initialTab="events" />
      </div>
    </DefaultLayout>
  );
}
